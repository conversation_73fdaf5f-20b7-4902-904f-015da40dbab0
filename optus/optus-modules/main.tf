# module "ecs_cluster" {
#     source = "./modules/ecs-cluster"
#     name   = var.app_name
# }

module "admin-dashboard" {
  source   = "./modules/admin-dashboard"
  app_name = var.app_name_admin_dashboard
}

module "imap" {
  source      = "./modules/imap"
  app_name    = var.app_name_imap
  vpc_id      = data.aws_vpc.optus-vpc.id
  ecs_cluster = data.aws_ecs_cluster.existing.cluster_name
}

module "freeswitch" {
  source      = "./modules/free-switch"
  app_name    = var.app_name_freeswitch
  vpc_id      = data.aws_vpc.optus-vpc.id
  alb_subnets = var.alb_subnets
  ecs_cluster = data.aws_ecs_cluster.existing.cluster_name
  fs_subnets  = var.fs_subnets
}

