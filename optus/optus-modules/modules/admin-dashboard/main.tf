# provider "aws" {
#   profile = "Optus"
#   region  = "ap-southeast-2"
# }
# # Create SSM parameters for app configuration (non-sensitive)
# resource "aws_ssm_parameter" "app_config" {
#   for_each = {
#     # Non-sensitive environment variables
#     "AWS_REGION"           = "ap-southeast-2"
#     "APNS_ENVIRONMENT"     = "sandbox"
#     "REDIS_MAX_CONNECTION" = "50"
#     "SMS_PROVIDER"         = "twilio"
#     "EMAIL_PROVIDER"       = "aws"
#     "AWS_S3_BUCKET_NAME"   = data.aws_s3_bucket.lab_mozart_artifacts_bkt.id
#     "TWILIO_ACCOUNT_SID"   = "AC91dd724da37a87d80a309b761d0128ad"
#     "SMS_FROM_NUMBER"      = "+***********"
#     "AWS_FROM_EMAIL"       = "<EMAIL>"

#     # Database and Redis connection strings with proper values
#     "DB_CONNECTION"        = "postgres://${data.aws_rds_cluster.rds.database_name}:${data.aws_secretsmanager_secret.rds_secret}@${data.aws_rds_cluster.rds.endpoint}/${data.aws_rds_cluster.rds.database_name}?sslmode=disable"
#     "REDIS_CONNECTION"     = "redis://${data.aws_elasticache_replication_group.redis-endpoint.primary_endpoint_address}:${data.aws_elasticache_replication_group.redis-endpoint.port}"
#   }

#   name        = "/${var.secret-ssm}/${each.key}"
#   description = "Mozart application configuration parameter"
#   type        = "String"
#   value       = each.value

#   tags = {
#     Name = "${var.secret-ssm}-${each.key}"
#   }
# }

# # Create secrets for sensitive values
# resource "aws_secretsmanager_secret" "app_secrets" {
#   for_each = {
#     # Add secrets (sensitive values)
#     "DB_PASSWORD"     = "db-password"
#     "AAI_API_KEY"     = "aai-api-key"
#     "AAI_HOSTNAME"    = "aai-hostname"
#     "AAI_WEBHOOK_URL" = "aai-webhook-url"
#     "APNS_KEY"        = "apns-key"
#     "JWT_SECRET"      = "jwt-secret"
#     "AUTH_PASSWORD"   = "admin-dashboard-auth-password"
#     "TWILIO_AUTH_TOKEN" = "twilio-auth-token"
#   }

#   name = "/${var.secret-ssm}/${each.key}"

#   tags = {
#     Name = "${var.secret-ssm}-${each.key}"
#   }
# }

# # Secret values for external services are defined in secrets.tf which should be in .gitignore
# # See secrets.tf.example for format reference

# # Handle DB password separately since it comes from the RDS module (no sensitive data is leaked)
# resource "aws_secretsmanager_secret_version" "db_password" {
#   secret_id     = aws_secretsmanager_secret.app_secrets["DB_PASSWORD"].id
#   secret_string = jsonencode({
#     password = data.aws_secretsmanager_secret.rds_secret
#   })
# }

# Admin dashboard resources
resource "aws_security_group" "admin_dashboard_alb" {
  name        = "${var.app_name}-alb-sg"
  description = "Allow HTTP inbound traffic for admin dashboard"
  vpc_id      = data.aws_vpc.optus-vpc.id

  ingress {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-alb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group" "admin_dashboard_tasks" {
  name        = "${var.app_name}-tasks-sg"
  description = "Allow inbound access from the ALB only for admin dashboard"
  vpc_id      = data.aws_vpc.optus-vpc.id

  ingress {
    protocol        = "tcp"
    from_port       = 4000
    to_port         = 4000
    security_groups = [aws_security_group.admin_dashboard_alb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-tasks-sg"
  }
}

resource "aws_lb" "admin_dashboard" {
  name               = "${var.app_name}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.admin_dashboard_alb.id]
  subnets            = var.alb_subnets

  tags = {
    Name = "${var.app_name}-alb"
  }
}

resource "aws_lb_target_group" "admin_dashboard" {
  name        = "${var.app_name}-target-group"
  port        = 4000
  protocol    = "HTTP"
  vpc_id      = data.aws_vpc.optus-vpc.id
  target_type = "ip"

  health_check {
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    path                = "/"
    port                = "traffic-port"
    matcher             = "200-499"
  }

  tags = {
    Name = "${var.app_name}-target-group"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "admin_dashboard" {
  load_balancer_arn = aws_lb.admin_dashboard.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.admin_dashboard.arn
  }
}

resource "aws_iam_role" "admin_dashboard_task_execution_role" {
  name = "${var.app_name}-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_iam_role_policy_attachment" "admin_dashboard_execution_role_policy" {
  role       = aws_iam_role.admin_dashboard_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_policy" "admin_dashboard_secrets_access" {
  name        = "${var.app_name}-secrets-access"
  description = "Allow admin dashboard ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "admin_dashboard_secrets_policy" {
  role       = aws_iam_role.admin_dashboard_task_execution_role.name
  policy_arn = aws_iam_policy.admin_dashboard_secrets_access.arn
}

resource "aws_iam_role" "admin_dashboard_task_role" {
  name = "${var.app_name}-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudwatch_log_group" "admin_dashboard" {
  name              = "/ecs/${var.app_name}"
  retention_in_days = 30

  tags = {
    Name = "${var.app_name}-log-group"
  }
}

resource "aws_ecs_task_definition" "admin_dashboard" {
  family                   = "${var.app_name}-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["EC2"]
  cpu                      = 2048
  memory                   = 2048
  execution_role_arn       = aws_iam_role.admin_dashboard_task_execution_role.arn
  task_role_arn            = aws_iam_role.admin_dashboard_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "${var.app_name}"
      image     = "${var.repository_url}:${var.image_tag}"
      essential = true
      portMappings = [
        {
          containerPort = 4000
          hostPort      = 4000
        }
      ]
      environment = [
        {
          name  = "PORT"
          value = "4000"
        }
      ]
      secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(data.aws_ssm_parameter.app_config) : {
            name      = key
            valueFrom = data.aws_ssm_parameter.app_config[key].arn
          }
        ],
        # Sensitive values from Secrets Manager
        [
          for i in range(length(data.aws_secretsmanager_secrets.all_secrets.names)) : {
            name      = data.aws_secretsmanager_secrets.all_secrets.names[i]
            valueFrom = data.aws_secretsmanager_secrets.all_secrets.arns[i]
          }
        ]
      )
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/${var.app_name}"
          "awslogs-region"        = "ap-southeast-2"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Name = "${var.app_name}-task-definition"
  }
}

resource "aws_ecs_service" "admin_dashboard" {
  name            = "${var.app_name}-service"
  cluster         = data.aws_ecs_cluster.existing.id
  task_definition = aws_ecs_task_definition.admin_dashboard.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.ec2.name
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.admin_dashboard_tasks.id]
    subnets          = var.alb_subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.admin_dashboard.arn
    container_name   = var.app_name
    container_port   = 4000
  }

  lifecycle {
    ignore_changes = [
      task_definition # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.admin_dashboard]

  tags = {
    Name = "${var.app_name}-service"
  }
}