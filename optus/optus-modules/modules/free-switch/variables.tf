variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "alb_subnets" {
  description = "List of subnet IDs for the load balancer"
  type        = list(string)
}

variable "fs_subnets" {
  description = "List of subnet IDs for the freeswitch instances"
  type        = list(string)
}

variable "ecs_cluster" {
  description = "Name of the ECS cluster"
  type        = string
}

variable "ecs_ami_id" {
  description = "AMI ID for ECS optimized instances"
  type        = string
  default     = "ami-083d56d17cad6cf58"
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.medium"
}
