provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
}


# Create secrets for sensitive values
resource "aws_secretsmanager_secret" "app_secrets" {
  for_each = {
#    # Add secrets (sensitive values)
    "DB_PASSWORD-LAB"     = "db-password"
#    "AAI_API_KEY_LAB"     = "aai-api-key"
#    "AAI_HOSTNAME_LAB"    = "aai-hostname"
#    "AAI_WEBHOOK_URL_LAB" = "aai-webhook-url"
#    "APNS_KEY_LAB"        = "apns-key"
#    "JWT_SECRET_LAB"      = "jwt-secret"
#    "AUTH_PASSWORD_LAB"   = "admin-dashboard-auth-password"
#    "TWILIO_AUTH_TOKEN_LAB" = "twilio-auth-token"
  }
#
  name = "/mozart-lab/${each.key}"
#  
  tags = {
    Name = "mozart-lab-${each.key}"
  }
}

data "aws_secretsmanager_secret_version" "rds_password" {
  secret_id = module.aurora_rds.password_secret_arn
}

# Create SSM parameters for app configuration (non-sensitive)
resource "aws_ssm_parameter" "app_config" {
  for_each = {
    # Database and Redis connection strings with proper values
    "DB_CONNECTION"        = "postgres://${module.aurora_rds.master_username}:${data.aws_secretsmanager_secret_version.rds_password.secret_string}@${module.aurora_rds.cluster_endpoint}/${module.aurora_rds.database_name}?sslmode=disable"
    "REDIS_CONNECTION"     = "redis://${module.elasticache.primary_endpoint}:${module.elasticache.port}"
  }

  name        = "/mozart-lab/${each.key}"
  description = "Mozart application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "mozart-lab${each.key}"
  }
}

# Handle DB password separately since it comes from the RDS module (no sensitive data is leaked)
resource "aws_secretsmanager_secret_version" "db_password" {
  secret_id     = aws_secretsmanager_secret.app_secrets["DB_PASSWORD-LAB"].id
  secret_string = jsonencode({
    password = data.aws_secretsmanager_secret_version.rds_password.secret_string
  })
}

# data "aws_secretsmanager_secret_version" "rds_password" {
#  secret_id = module.aurora_rds.password_secret_arn
#}

module "aurora_rds" {
  source         = "./modules/aurora_rds"
  app_name       = "mozart-db-lab"
}

module "kamailio_rds" {
  source         = "./modules/kamailio_rds"
  app_name       = "kamailio-db-lab"
}

module "elasticache" {
  source         = "./modules/elasticache"
  app_name       = "mozart-redis-lab"

}  
