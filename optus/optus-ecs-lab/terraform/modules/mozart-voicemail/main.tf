# Mozart Voicemail server resources


data "aws_launch_template" "existing" {
  filter {
    name   = "tag:Name"
    values = ["mozart-lab-launch_template"]
  }
}

# Access them in a local variable (easier reference)
locals {
  ssm_parameters = {
    "AWS_REGION"           = "/mozart-lab/AWS_REGION"
    "APNS_ENVIRONMENT"     = "/mozart-lab/APNS_ENVIRONMENT"
    "REDIS_MAX_CONNECTION" = "/mozart-lab/REDIS_MAX_CONNECTION"
    "SMS_PROVIDER"         = "/mozart-lab/SMS_PROVIDER"
    "EMAIL_PROVIDER"       = "/mozart-lab/EMAIL_PROVIDER"
    "AWS_S3_BUCKET_NAME"   = "/mozart-lab/AWS_S3_BUCKET_NAME"
    "TWILIO_ACCOUNT_SID"   = "/mozart-lab/TWILIO_ACCOUNT_SID"
    "SMS_FROM_NUMBER"      = "/mozart-lab/SMS_FROM_NUMBER"
    "AWS_FROM_EMAIL"       = "/mozart-lab/AWS_FROM_EMAIL"
  }
}

data "aws_ssm_parameter" "app_config" {
  for_each = local.ssm_parameters
  name     = each.value
}

data "aws_ecs_cluster" "existing" {
  cluster_name = "mozart-lab-cluster"
}

resource "aws_autoscaling_group" "ecs-lab-voicemail" {
  name                 = "voicemail-lab-asg"
  vpc_zone_identifier  = var.subnets
  # launch_template = data.aws_launch_template.existing.id
  min_size             = 1
  max_size             = 2
  desired_capacity     = 1

  launch_template {
    id      = data.aws_launch_template.existing.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-ecs-instance"
    propagate_at_launch = true
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = ""
    propagate_at_launch = true
  }
  depends_on = [data.aws_launch_template.existing]
}

resource "aws_ecs_capacity_provider" "ec2" {
  name = "${var.app_name}-ec2-capacity-provider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.ecs-lab-voicemail.arn
    
    managed_scaling {
      maximum_scaling_step_size = 1000
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }
}


resource "aws_iam_policy" "voicemail_secrets_access" {
  name        = "voicemail-lab-secrets-access"
  description = "Allow voicemail server ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_security_group" "voicemail_alb" {
  name        = "voicemail-lab-alb-sg"
  description = "Allow HTTP/HTTPS inbound traffic "
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "voicemail-lab-alb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb" "voicemail" {
  name               = "voicemail-lab-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.voicemail_alb.id]
  subnets            = var.alb_subnets

  tags = {
    Name = "voicemail-lab-alb"
  }
}

resource "aws_security_group" "voicemail" {
  name        = "voicemail-lab-sg"
  description = "Allow inbound access from the ALB only to voicemail server"
  vpc_id      = var.vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = 8080
    to_port         = 8080
    security_groups = [aws_security_group.voicemail_alb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "voicemail-lab-sg"
  }
}

resource "aws_lb_target_group" "voicemail" {
  name        = "voicemail-lab-target-group"
  port        = 8080
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    # command             = ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
    path                = "/health"
    port                = 8080
    matcher             = "200-299"
  }

  tags = {
    Name = "voicemail-lab-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "voicemail" {
  load_balancer_arn = aws_lb.voicemail.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.voicemail.arn
  }
}


resource "aws_ecs_task_definition" "voicemail" {
  family                   = "voicemail-lab-task"
  network_mode             = "awsvpc"  # or "host" if using host networking
  requires_compatibilities = ["EC2"]   # or ["FARGATE"] if using Fargate
  execution_role_arn       = aws_iam_role.voicemail_execution_role.arn
  task_role_arn            = aws_iam_role.voicemail_task_role.arn
  cpu                      = var.cpu      # 2 vCPU
  memory                   = var.memory      # 4GB memory

  container_definitions = jsonencode([{
    name      = "voicemail-lab"
    image     = "${var.repository_url}:${var.image_tag}"
    essential = true

    portMappings = [
        {
          containerPort = var.voicemail_container_port,
          protocol = "tcp"
          # hostPort      = var.voicemail_container_port
        }
      ]
    environment = concat(
        [
          {
            name  = "PORT"
            value = tostring(var.voicemail_container_port)
          }
        ],
        var.environment_variables
      ),
      secrets = var.secrets,
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = "/ecs/voicemail-lab"
        awslogs-region        = "ap-southeast-2"
        awslogs-stream-prefix = "voicemail"
      }
    }
  }])
}

resource "aws_ecs_service" "voicemail" {
  name            = "voicemail-lab-service"
  cluster         = data.aws_ecs_cluster.existing.id
  task_definition = aws_ecs_task_definition.voicemail.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = "${var.app_name}-ec2-capacity-provider"
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.voicemail.id]
    subnets          = var.subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.voicemail.arn
    container_name   = "voicemail-lab"
    container_port   = 8080
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.voicemail]

  tags = {
    Name = "voicemail-lab-service"
  }
}

# Supporting Resources

resource "aws_iam_role" "voicemail_execution_role" {
  name = "ecs-voicemail-lab-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "voicemail_execution_role_policy" {
  role       = aws_iam_role.voicemail_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "voicemail_secrets_policy" {
  role       = aws_iam_role.voicemail_execution_role.name
  policy_arn = aws_iam_policy.voicemail_secrets_access.arn
}

resource "aws_iam_role" "voicemail_task_role" {
  name = "ecs-voicemail-lab-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_cloudwatch_log_group" "voicemail" {
  name              = "/ecs/voicemail-lab"
  retention_in_days = 7
}
